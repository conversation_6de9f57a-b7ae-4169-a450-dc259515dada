package ai.yourouter.calculate.search;


import ai.yourouter.common.utils.OrganizationBalanceUtils;
import ai.yourouter.common.utils.SnowflakeIdGenerator;
import ai.yourouter.common.utils.SystemClock;
import ai.yourouter.jpa.organization.cycle.repository.OrganizationSearchCycleRepository;
import ai.yourouter.jpa.organization.dailybill.repository.OrganizationSearchDailyBillRepository;
import ai.yourouter.jpa.organization.statistics.repository.OrganizationSearchStatisticsRepository;
import ai.yourouter.jpa.system.model.info.bean.SystemModelInfo;
import ai.yourouter.jpa.system.model.info.repository.SystemModelInfoRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.time.ZoneOffset;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class OrganizationSearchCycleTimer {

    private final SnowflakeIdGenerator snowflakeIdGenerator;

    private final SystemModelInfoRepository systemModelInfoRepository;

    private final OrganizationSearchStatisticsRepository organizationSearchStatisticsRepository;

    private final OrganizationSearchCycleRepository organizationSearchCycleRepository;

    private final OrganizationSearchDailyBillRepository organizationSearchDailyBillRepository;

    private static final Duration WINDOW = Duration.ofMinutes(5);

    private final OrganizationBalanceUtils organizationBalanceUtils;

    @Scheduled(cron = "0 0/5 * * * *")
    @Transactional(rollbackFor = Exception.class)
    public void doAggregate() {
        log.info("OrganizationSearchCycleTimer started");

        long now      = SystemClock.now();
        long cutOff   = (now / WINDOW.toMillis()) * WINDOW.toMillis() - 1;

        /* ========== A 锁行+聚合 ========== */
        List<Object[]> rows = organizationSearchStatisticsRepository.lockAndAggregate(cutOff);

        if (rows.isEmpty()) return;
        /* ========== B 预加载模型名称 ========== */
        Map<Long, String> modelNames = systemModelInfoRepository.findAllById(
                rows.stream().map(r -> (Long) r[1]).collect(Collectors.toSet())
        ).stream().collect(Collectors.toMap(SystemModelInfo::getId,
                SystemModelInfo::getModelName));

        Map<Long, BigDecimal> quotaTotals = new HashMap<>();

        /* ========== C 写 Cycle & Daily，并记住 ids ========== */
        for (Object[] r : rows) {
            int p = 0;
            Long orgId       = (Long)      r[p++];
            Long modelId     = (Long)      r[p++];
            long bucketStart = ((Number)   r[p++]).longValue();
            long reqCnt      = ((Number)   r[p++]).longValue();
            long callCnt     = ((Number)   r[p++]).longValue();
            BigDecimal quota = (BigDecimal) r[p++];
            Long[] ids       = (Long[])     r[p++];

            organizationSearchCycleRepository.upsertCycle(
                    snowflakeIdGenerator.nextId(), orgId, modelId, bucketStart,
                    reqCnt, callCnt, quota);

            long billDay = Instant.ofEpochMilli(bucketStart)
                    .atOffset(ZoneOffset.UTC)
                    .toLocalDate()
                    .toEpochDay() * 86_400_000L;

            organizationSearchDailyBillRepository.upsertDaily(
                    snowflakeIdGenerator.nextId(), orgId, modelNames.get(modelId),
                    billDay, now, reqCnt, callCnt, quota);

            /* --- 钱包扣费（事务外也可） --- */
            quotaTotals.merge(orgId, quota, BigDecimal::add);

            // 状态 2 → 1
            organizationSearchStatisticsRepository.markDone(ids);
        }

        for (Map.Entry<Long, BigDecimal> e : quotaTotals.entrySet()) {
            organizationBalanceUtils.consumeBalance(e.getKey(), e.getValue());
        }

    }
}
