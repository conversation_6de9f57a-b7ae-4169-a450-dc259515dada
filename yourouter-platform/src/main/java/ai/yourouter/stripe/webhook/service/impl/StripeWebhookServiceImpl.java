package ai.yourouter.stripe.webhook.service.impl;

import ai.yourouter.common.utils.BillingUtils;
import ai.yourouter.common.utils.OrganizationBalanceUtils;
import ai.yourouter.jpa.organization.transaction.balance.bean.OrganizationBillingStatement;
import ai.yourouter.jpa.organization.transaction.balance.repository.OrganizationBillingStatementRepository;
import com.stripe.exception.SignatureVerificationException;
import com.stripe.model.*;
import com.stripe.model.checkout.Session;
import com.stripe.net.Webhook;
import ai.yourouter.common.utils.SnowflakeIdGenerator;
import ai.yourouter.common.utils.SystemClock;
import ai.yourouter.jpa.organization.transaction.balance.bean.BalanceTransaction;
import ai.yourouter.jpa.organization.transaction.balance.repository.BalanceTransactionRepository;
import ai.yourouter.jpa.stripe.record.bean.StripeChargeRecord;
import ai.yourouter.jpa.stripe.record.repository.StripeChargeRecordRepository;
import io.micrometer.common.util.StringUtils;
import lombok.RequiredArgsConstructor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import ai.yourouter.stripe.webhook.service.StripeWebhookService;

import java.math.BigDecimal;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class StripeWebhookServiceImpl implements StripeWebhookService {

    @Value("${stripe.webhook.secret}")
    private String webhookSecret;

    private final SnowflakeIdGenerator snowflakeIdGenerator;

    private final StripeChargeRecordRepository stripeChargeRecordRepository;

    private final BalanceTransactionRepository balanceTransactionRepository;

    private final OrganizationBalanceUtils organizationBalanceUtils;

    private final OrganizationBillingStatementRepository organizationBillingStatementRepository;


    @Override
    public ResponseEntity<String> handleWebhook(String payload, String sigHeader) {
        Event event = null;
        try {
            // 验证签名并构造 Event 对象
            event = Webhook.constructEvent(payload, sigHeader, webhookSecret);
        } catch (SignatureVerificationException e) {
            // 签名验证失败
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Invalid signature");
        }

        String type = event.getType();
        log.info("📥  Stripe webhook received: {}", type);

        switch (type) {
            /* 1) Checkout 完成（推荐）：最可靠 */
            case "checkout.session.completed" -> handleSessionCompleted(event);

//            /* 2) 备用：直接监听 charge.succeeded 也可 */
//            case "charge.succeeded" -> handleChargeSucceeded(event);
//
//            /* 3) 支付失败 */
//            case "payment_intent.payment_failed" -> handlePaymentFailed(event);
//
//            /* 4) 捕获、退款等 */
//            case "charge.refunded", "refund.updated" -> handleRefund(event);

            default -> log.debug("Unprocessed event type {}", type);
        }
        return ResponseEntity.ok("received");
    }

    /* ------------------ 事件处理私有方法 ------------------ */

    /** checkout.session.completed → status=SUCCEEDED & paymentIntentId 等 */
    private void handleSessionCompleted(Event event) {
        Session session = (Session) deserialize(event);

        String sessionId = session.getId(); // 作为业务幂等键
        String localId = Optional.ofNullable(session.getMetadata())
                .map(m -> m.get("localChargeId"))
                .orElseThrow(() -> new IllegalStateException("missing localChargeId in session.metadata"));



        if (balanceTransactionRepository.existsByStripeChargeId(sessionId)) {
            log.info("skip duplicate checkout.session.completed, sessionId={}", sessionId);
            // 只做 record 字段的幂等回填（可选）
            StripeChargeRecord existed = findCharge(Long.valueOf(localId), sessionId);
            safeFillChargeRecordFromSession(existed, session); // 见下方方法
            stripeChargeRecordRepository.save(existed);
            return;
        }


        StripeChargeRecord chargeRecord = findCharge(Long.valueOf(localId), session.getId());



        chargeRecord.setAmount(BigDecimal.valueOf(session.getAmountTotal()).divide(BigDecimal.valueOf(100)));
        // ✅ 状态与关键标识写入
        chargeRecord.setStatus("SUCCEEDED");
        chargeRecord.setCustomerId(session.getCustomer());
        chargeRecord.setPaymentIntentId(session.getPaymentIntent());

        // ✅ 安全设置 paymentMethodId（session.getPaymentMethodOptions() 可能为 null）
        if (session.getPaymentMethodOptions() != null) {
            chargeRecord.setPaymentMethodId(session.getPaymentMethodOptions().toJson());
        }

        // ✅ 邮箱、收据写入
        chargeRecord.setReceiptEmail(session.getCustomerEmail());

        // ✅ 写入原始 JSON（可选）
        chargeRecord.setMetadata(session.toJson());

        // ✅ 保存支付记录
        stripeChargeRecordRepository.save(chargeRecord);

        // ✅ 生成 balance transaction（金额单位 = cents）
        balanceTransactionRepository.save(new BalanceTransaction(
                snowflakeIdGenerator.nextId(),
                chargeRecord.getOrganizationId(),
                BalanceTransaction.TransactionType.RECHARGE,
                chargeRecord.getAmount(),
                BigDecimal.ZERO,
                BigDecimal.ZERO,
                Boolean.FALSE,
                chargeRecord.getStripeId(),
                SystemClock.now(),
                String.valueOf(chargeRecord.getId()),
                session.toJson()
        ));

        //做总账
        OrganizationBillingStatement organizationBillingStatement = organizationBillingStatementRepository.findOrganizationBillingStatementByOrganizationId(chargeRecord.getOrganizationId()).get();
        organizationBillingStatement.setTotalRecharge(organizationBillingStatement.getTotalRecharge().add(chargeRecord.getAmount()));
        organizationBillingStatement.setNetBalanceChange(organizationBillingStatement.getNetBalanceChange().add(chargeRecord.getAmount()));
        organizationBillingStatementRepository.save(organizationBillingStatement);

        // ✅ 组织账户余额 + nanoCent（需要转成美元 BigDecimal）

        organizationBalanceUtils.addBalance(
                chargeRecord.getOrganizationId(),
                BillingUtils.usdToNanoCent(chargeRecord.getAmount())
        );

        // ✅ 日志更详细
        log.info("✅ Charge {} marked SUCCEEDED — ${}, customerId={}, orgId={}",
                chargeRecord.getId(),
                chargeRecord.getAmount(),
                chargeRecord.getCustomerId(),
                chargeRecord.getOrganizationId()
        );

    }

    /* ------------------ 工具函数 ------------------ */

    /** 反序列化事件对象 */
    private Object deserialize(Event event) {
      return   event.getData().getObject();
    }

    /**
     * 根据 localChargeId（优先）或 session/charge ID 从库里找记录
     */
    private StripeChargeRecord findCharge(Long localId, String stripeRefId) {
        return (localId != null)
                ? stripeChargeRecordRepository.findById(localId)
                .orElseThrow(() -> new IllegalStateException("Charge not found by localId " + localId))
                : stripeChargeRecordRepository.findStripeChargeRecordByStripeId(stripeRefId)
                .orElseThrow(() -> new IllegalStateException("Charge not found by stripeId " + stripeRefId));
    }

    private void safeFillChargeRecordFromSession(StripeChargeRecord r, Session s) {
        r.setStatus("SUCCEEDED");
        r.setCustomerId(s.getCustomer());
        r.setReceiptEmail(s.getCustomerEmail());
        if (s.getPaymentMethodOptions() != null) {
            r.setPaymentMethodId(s.getPaymentMethodOptions().toJson());
        }
        r.setMetadata(s.toJson());
    }
}
