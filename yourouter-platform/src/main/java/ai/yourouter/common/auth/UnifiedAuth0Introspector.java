package ai.yourouter.common.auth;

import ai.yourouter.common.constant.SystemConstant;
import ai.yourouter.jpa.organization.transaction.balance.bean.OrganizationBillingStatement;
import ai.yourouter.jpa.organization.transaction.balance.repository.OrganizationBillingStatementRepository;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import ai.yourouter.common.constant.PermissionStatusEnum;
import ai.yourouter.common.constant.RedisKey;
import ai.yourouter.common.constant.UnpaidStatus;
import ai.yourouter.common.utils.BillingUtils;
import ai.yourouter.common.utils.SnowflakeIdGenerator;
import ai.yourouter.common.utils.SystemClock;
import com.stripe.Stripe;
import com.stripe.exception.StripeException;
import com.stripe.model.Customer;
import com.stripe.param.CustomerCreateParams;
import jakarta.servlet.http.HttpServletRequest;
import ai.yourouter.jpa.organization.info.bean.OrganizationInfo;
import ai.yourouter.jpa.organization.info.repository.OrganizationInfoRepository;
import ai.yourouter.jpa.organization.team.bean.OrganizationTeam;
import ai.yourouter.jpa.organization.team.repository.OrganizationTeamRepository;
import ai.yourouter.jpa.organization.transaction.balance.bean.BalanceTransaction;
import ai.yourouter.jpa.organization.transaction.balance.repository.BalanceTransactionRepository;
import ai.yourouter.jpa.user.info.bean.UserInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.*;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.core.DefaultOAuth2AuthenticatedPrincipal;
import org.springframework.security.oauth2.core.OAuth2AuthenticatedPrincipal;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.OAuth2Error;
import org.springframework.security.oauth2.core.oidc.StandardClaimNames;
import org.springframework.security.oauth2.server.resource.introspection.OpaqueTokenIntrospector;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import ai.yourouter.user.info.service.UserInfoService;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;

@Slf4j
@Component
@RequiredArgsConstructor
public class UnifiedAuth0Introspector implements OpaqueTokenIntrospector {



    private final UserInfoService userInfoService;

    private final OrganizationInfoRepository organizationInfoRepository;

    private final SnowflakeIdGenerator snowflakeIdGenerator;

    private final BalanceTransactionRepository balanceTransactionRepository;

    private final OrganizationTeamRepository organizationTeamRepository;

    private final CurrentUser currentUser;

    private final ThreadLocalUserPermissionContext threadLocalUserPermissionContext;

    private final RedisTemplate<String, String> redisTemplate;

    private final AuthCacheService authCacheService;

    private final OrganizationBillingStatementRepository organizationBillingStatementRepository;

    @Override
    public OAuth2AuthenticatedPrincipal introspect(String token) {

        HttpServletRequest req = getCurrentHttpRequest();

        String orgParam = req.getParameter("organizationId");
        Long requestOrgId = (orgParam == null || orgParam.isBlank()) ? null : Long.valueOf(orgParam);

        /* 1. 调 Auth0 /userinfo，结果按 token 缓存 15 min */
        Map<String, Object> claims = authCacheService.fetchUserInfo(token);

        /* 2. 本地用户同步 */
        UserInfo user = userInfoService.ensureUser(claims);
        currentUser.set(user);   // basic identity
        threadLocalUserPermissionContext.setUserInfo(user);  // 设置到 ThreadLocal 权限上下文

        /* 3. 组织权限逻辑 */
        boolean hasAnyBind = organizationTeamRepository.existsByUserId(user.getId());

        // 3.1 若前端带 organizationId，就按该组织查绑定
        if (requestOrgId != null) {
            setPermissionIfBound(user.getId(), requestOrgId);
        }

        // 3.2 若用户完全没有任何组织绑定 → 自动创建一个组织
        if (!hasAnyBind) {
            log.info("User {} has no organization, binding to default org ", user.getId());

            Long time = SystemClock.now();
            CustomerCreateParams params = CustomerCreateParams.builder()
                    .setEmail(user.getEmail())
                    .setName(user.getNickname())
                    .build();
            Customer customer = null;
            try {
                customer = Customer.create(params);
            } catch (StripeException e) {
                throw new RuntimeException(e);
            }

            // 3. 从 Customer 对象中获取 ID
            String customerId = customer.getId();

            OrganizationInfo organizationInfo = new OrganizationInfo(snowflakeIdGenerator.nextId(), "default", customerId, time, PermissionStatusEnum.AVAILABLE.getCode());
            BalanceTransaction balanceTransaction = new BalanceTransaction(snowflakeIdGenerator.nextId(), organizationInfo.getId(), BalanceTransaction.TransactionType.GIFT, BigDecimal.valueOf(5l),BigDecimal.ZERO,BigDecimal.ZERO,Boolean.FALSE, null, time, null, "A $5 bonus is credited to your account upon registration.");
            OrganizationBillingStatement organizationBillingStatement = new OrganizationBillingStatement(snowflakeIdGenerator.nextId(),organizationInfo.getId(),SystemClock.now(),SystemClock.now(),BigDecimal.ZERO, BigDecimal.valueOf(5L),BigDecimal.ZERO,BigDecimal.ZERO,BigDecimal.ZERO,BigDecimal.valueOf(5L), Instant.now());


            // 创建组织团队关系，新用户默认为管理员权限(1)
            Integer adminPermission = PermissionStatusEnum.AVAILABLE.getCode(); // 1 = 管理员
            OrganizationTeam organizationTeam = new OrganizationTeam(snowflakeIdGenerator.nextId(), user.getId(), organizationInfo.getId(), time, adminPermission, PermissionStatusEnum.AVAILABLE.getCode());

            log.info("Creating new organization team - userId: {}, orgId: {}, permission: {}",
                    user.getId(), organizationInfo.getId(), adminPermission);

            organizationInfoRepository.save(organizationInfo);
            balanceTransactionRepository.save(balanceTransaction);
            organizationTeamRepository.save(organizationTeam);
            organizationBillingStatementRepository.save(organizationBillingStatement);
            log.info("Setting user permission to: {}", adminPermission);

            // 设置权限上下文到 ThreadLocal
            threadLocalUserPermissionContext.setUserPermission(user.getId(), organizationInfo.getId(), adminPermission);

            // 立即验证设置结果
            Integer contextPermission = threadLocalUserPermissionContext.getPermission();
            log.info("Permission verification after new user creation - expected: {}, actual: {}",
                    adminPermission, contextPermission);

            redisTemplate.opsForValue().increment(SystemConstant.ORGANIZATION_REDIS_BALANCE + organizationInfo.getId(), BillingUtils.usdToNanoCent(BigDecimal.valueOf(5L)));
            redisTemplate.opsForValue().set(RedisKey.ORGANIZATION_UNPAID_STATUS.getKey(organizationInfo.getId()), String.valueOf(UnpaidStatus.UNAVAILABLE));
            redisTemplate.opsForValue().set(RedisKey.ORGANIZATION_PERMISSION_STATUS.getKey(organizationInfo.getId()), String.valueOf(PermissionStatusEnum.AVAILABLE.getCode()));
        }


        /* ---------- 4. 组装 Principal 返回给 Spring Security ---------- */
        // 最终权限状态检查
        Integer finalPermission = threadLocalUserPermissionContext.getPermission();
        log.info("Authentication completed - userId: {}, requestOrgId: {}, finalPermission: {}",
                user.getId(), requestOrgId, finalPermission);

        Collection<GrantedAuthority> authorities =
                Collections.singletonList(new SimpleGrantedAuthority("ROLE_USER"));
        String principalName = Optional.ofNullable(claims.get(StandardClaimNames.SUB))
                .map(Object::toString).orElse("unknown");

        return new DefaultOAuth2AuthenticatedPrincipal(principalName, claims, authorities);
    }




    private void setPermissionIfBound(Long userId, Long orgId) {
        log.info("setPermissionIfBound called - userId: {}, orgId: {}", userId, orgId);

        OrganizationTeam bind = organizationTeamRepository.findOrganizationTeamByUserIdAndOrganizationIdAndStatuses(userId, orgId, PermissionStatusEnum.AVAILABLE.getCode());
        if (bind != null) {
            log.info("Found organization bind - permission: {}", bind.getPermission());

            // 设置权限上下文到 ThreadLocal
            threadLocalUserPermissionContext.setUserPermission(userId, orgId, bind.getPermission());

            // 验证设置是否成功
            Integer contextPermission = threadLocalUserPermissionContext.getPermission();
            log.info("Permission set result - expected: {}, actual: {}",
                    bind.getPermission(), contextPermission);
        } else {
            log.debug("User {} not bound to organization {}", userId, orgId);
        }
    }
    /**
     * 拿到当前线程中的 HttpServletRequest
     */
    private HttpServletRequest getCurrentHttpRequest() {
        ServletRequestAttributes attrs =
                (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attrs == null) {
            throw new IllegalStateException("No HTTP request context");
        }
        return attrs.getRequest();
    }
}
