"use client"

import * as React from "react"
import {
    BookOpen,
    CreditCard,
    SquareTerminal,
    Users2,
} from "lucide-react"


import {
    Sidebar,
    SidebarContent,
    SidebarFooter,
    SidebarHeader,
} from "@/components/ui/sidebar"
import { NavMain } from "./nav-main"
import { NavSecondary } from "./nav-secondary"
import { NavUser } from "./nav-user"
import { TeamSwitcher } from "./team-switcher"
import { Separator } from "../ui/separator"

const data = {
    user: {
        name: "shadcn",
        email: "<EMAIL>",
        avatar: "/avatars/shadcn.jpg",
    },
    navMain: [
        {
            title: "Dashboard",
            url: "/dashboard/home",
            icon: SquareTerminal,
        },
        {
            title: "Team",
            url: "/dashboard/team",
            icon: Users2
        },
        {
            title: "Wallet",
            url: "/dashboard/wallet",
            icon: CreditCard
        },
        {
            title: "Secret",
            url: "/dashboard/secret",
            icon: BookOpen,
        },
    ],
    navSecondary: [

    ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
    return (
        <Sidebar variant="inset" {...props}>
            <SidebarHeader>
                <TeamSwitcher />
            </SidebarHeader>
            <Separator className="bg-transparent border-t border-dashed border-border" />
            <SidebarContent>
                <NavMain items={data.navMain} />
                <NavSecondary items={data.navSecondary} className="mt-auto" />
            </SidebarContent>
            <Separator className="bg-transparent border-t border-dashed border-border" />
            <SidebarFooter>
                <NavUser />
            </SidebarFooter>
        </Sidebar >
    )
}
