import { apiClient } from "@/lib/apiClient"
import type { OrganizationWalletRecord, UpdateAutoWalletParams, WalletResponse } from "./wallet-model"

export const WalletApi = {

    /**
     * Query transaction records
     * @param organizationId Organization ID
     * @returns Transaction record list
     */
    getOrganizationWalletRecord: (organizationId: string) => {
        return apiClient.get<OrganizationWalletRecord[]>({
            url: "/api/private/organizationWallet/changeRecords",
            params: {
                organizationId
            }
        })
    },

    /**
     * Set minimum amount recharge
     * @param params Update auto wallet parameters
     * @returns Operation result
     */
    updateAutoWallet: (params: UpdateAutoWalletParams) => {
        return apiClient.put<string>({
            url: "/private/organizationWallet/updateAutoWallet",
            params: {
                organizationId: params.organizationId,
                minimumAmount: params.minimumAmount,
                rechargeAmount: params.rechargeAmount
            }
        })
    },

    /**
     * Query balance
     * @param organizationId Organization ID
     * @returns Balance
     */
    getBalance: (organizationId: string): Promise<WalletResponse> => {
        return apiClient.get<WalletResponse>({
            url: "/private/organizationWallet/getWalletBalance",
            params: {
                organizationId
            }
        })
    },

    addPayment: (organizationId: string, amount: number) => {
        return apiClient.post({
            url: "/private/stripePayment/payment",
            params: {
                organizationId,
                amount
            }
        })
    },

    /**
     * Create Stripe checkout URL for recharge
     * @param organizationId Organization ID
     * @param amount Amount to recharge (in dollars)
     * @returns Checkout URL
     */
    createStripeCheckout: (organizationId: string, amount: number): Promise<{ checkoutUrl: string }> => {
        return apiClient.post<{ checkoutUrl: string }>({
            url: "/private/stripe/payment/checkout",
            params: {
                organizationId: organizationId,
                amount: Math.round(amount * 100) // Convert dollars to cents for backend
            }
        })
    },

    /**
     * Export bill
     * @param organizationId Organization ID
     * @param start Start time yyyy-MM-dd
     * @param end End time yyyy-MM-dd
     * @returns Bill
     */
    exportBill: (organizationId: string, start: string, end: string) => {
        return apiClient.post<string>({
            url: "/private/organizationDailyBill/download",
            params: {
                organizationId,
                start,
                end
            },
            responseType: 'blob',
        })
    }
}