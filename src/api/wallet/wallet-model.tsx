export interface OrganizationWalletRecord {
    id: string;
    organizationId: string;
    organizationWalletId: string;
    amountOfMoney: string;
    /**
     *  0 Gift
     *  1 Charge
     *  2 Refund
     */
    amountOfMoneyType: number;
    createTime: string;
    statuses: number;
}

// Update auto wallet parameters interface
export interface UpdateAutoWalletParams {
    organizationId: string;
    minimumAmount: number;
    rechargeAmount: number;
}


export interface WalletResponse {
    balance: number;
    minimumAmount: number;
    rechargeAmount: number;
    payment: boolean;
}