package ai.yourouter.jpa.system.model.info.repository;

import ai.yourouter.jpa.system.model.info.bean.SystemModelInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SystemModelInfoRepository extends JpaRepository<SystemModelInfo,Long> {

    public SystemModelInfo findSystemModelInfoByModelName(String modelName);

    List<SystemModelInfo> findSystemModelInfosByStatuses(Integer statuses);
}
