package ai.yourouter.jpa.organization.statistics.repository;

import ai.yourouter.jpa.organization.statistics.bean.OrganizationSearchStatistics;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

@Repository
public interface OrganizationSearchStatisticsRepository extends JpaRepository<OrganizationSearchStatistics,Long> {

    /**
     * 步骤 A：把需要处理的行一次性改 0→2 并返回聚合结果
     */
    @Query(value = """
        WITH moved AS (
            UPDATE sp_platform_organization_search_statistics
               SET statuses = 2
             WHERE statuses      = 0
               AND bucket_start <= :cutOff
             RETURNING id,
                       organization_id,
                       system_model_id,
                       bucket_start,
                       call,
                       quota
        )
        SELECT organization_id,
               system_model_id,
               bucket_start,
               COUNT(*)              AS reqCnt,
               SUM(call)             AS callCnt,
               SUM(quota)            AS totalQuota
               array_agg(id)         AS ids        -- ← 处理完后改成 1
        FROM moved
        GROUP BY organization_id, system_model_id, bucket_start
        """, nativeQuery = true)
    List<Object[]> lockAndAggregate(@Param("cutOff") long cutOff);


    /**
     * 步骤 C：将本批 id 改 2→1
     */
    @Modifying
    @Query(value = """
        UPDATE sp_platform_organization_search_statistics
           SET statuses = 1
         WHERE id = ANY(:ids)
        """, nativeQuery = true)
    int markDone(@Param("ids") Long[] ids);


    @Query(value = """
    SELECT COALESCE(SUM(quota), 0)
      FROM sp_platform_organization_search_statistics
     WHERE organization_id = :orgId
    """, nativeQuery = true)
    BigDecimal sumQuotaByOrganizationId(@Param("orgId") Long orgId);

}
