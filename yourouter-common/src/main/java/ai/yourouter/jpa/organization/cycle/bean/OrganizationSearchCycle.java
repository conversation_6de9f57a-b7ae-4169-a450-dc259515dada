package ai.yourouter.jpa.organization.cycle.bean;

import jakarta.persistence.*;
import lombok.*;

import java.math.BigDecimal;


@Entity
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Cacheable(value = true)
@Table(name = "sp_platform_organization_search_cycle")
public class OrganizationSearchCycle {

    @Id
    private Long id;

    private Long organizationId;

    private Long systemModelId;

    private Long channelKeysId;

    private Long call;

    @Column(precision = 29, scale = 18)
    private BigDecimal quota;

    public Long createTime;

    @Column(name = "bucket_start", insertable = false, updatable = false,columnDefinition = "BIGINT GENERATED ALWAYS AS ((create_time / 300000) * 300000) STORED")
    private Long bucketStart;         // 桶起点，数据库计算

    private Integer statuses;
}
