package ai.yourouter.jpa.organization.statistics.bean;

import jakarta.persistence.*;
import lombok.*;

import java.math.BigDecimal;

@Entity
@Getter
@Setter
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Cacheable(value = true)
@Table(name = "sp_platform_organization_llm_statistics")
public class OrganizationLLMStatistics {

    @Id
    private Long id;

    private Long organizationId;

    private Long systemModelId;

    private String systemVendor;

    private Long textPrompt;

    private Long textCachePrompt;

    private Long textCachePromptWrite5M;

    private Long textCachePromptWrite1H;

    private Long textCompletion;

    private Long audioPrompt;

    private Long audioCachePrompt;

    private Long audioCompletion;

    private Long reasoningCompletion;

    private Long imagePrompt;

    private Long imageCachePrompt;

    private Long imageCompletion;

    @Column(precision = 29, scale = 18)
    private BigDecimal quota;

    private String requestId;

    public Long createTime;

    private Long requestTime;   // 新增

    private Long responseTime;  // 新增

    // 若希望把 duration_ms 读出来，可再加：
    private Long durationMs;

    @Column(name  = "bucket_start", insertable = false, updatable = false, columnDefinition = "BIGINT GENERATED ALWAYS AS ((create_time / 300000) * 300000) STORED")
    private Long bucketStart;         // 桶起点，数据库计算

    private Integer statuses;

    private Long keyId;
}
