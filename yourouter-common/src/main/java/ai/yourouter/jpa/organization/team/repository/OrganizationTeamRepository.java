package ai.yourouter.jpa.organization.team.repository;

import ai.yourouter.jpa.organization.team.bean.OrganizationTeam;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OrganizationTeamRepository extends JpaRepository<OrganizationTeam, String> {


    OrganizationTeam findOrganizationTeamByUserIdAndOrganizationIdAndStatuses(Long userId, Long organizationId, Integer statuses);

    List<OrganizationTeam> findOrganizationTeamsByUserIdAndStatuses(Long userId, Integer statuses);

    OrganizationTeam findOrganizationTeamByOrganizationIdAndUserId(Long organizationId, Long userId);

    List<OrganizationTeam> findOrganizationTeamsByOrganizationIdAndStatuses(Long organizationId, Integer statuses);

    List<OrganizationTeam> findOrganizationTeamsByOrganizationIdAndPermissionAndStatuses(Long organizationId, Integer permission, Integer statuses);

    Boolean existsByUserId(Long userId);
}
