package ai.yourouter.jpa.organization.dailybill.repository;

import ai.yourouter.jpa.organization.dailybill.bean.OrganizationLLMDailyBill;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;

@Repository
public interface OrganizationLLMDailyBillRepository extends JpaRepository<OrganizationLLMDailyBill, Long> {


    @Modifying
    @Query(value = """
        INSERT INTO sp_platform_organization_llm_daily_bill
            (id, organization_id, model_name, bill_day, create_time,
             text_prompt, text_cache_prompt, text_cache_prompt_write5m,
             text_cache_prompt_write1h, text_completion,
             audio_prompt, audio_cache_prompt, audio_completion,
             reasoning_completion,
             image_prompt, image_cache_prompt, image_completion,
             request, quota)
        VALUES
            (:id, :orgId, :modelName, :billDay, :now<PERSON>ill<PERSON>,
             :tp, :tcp, :tcp5, :tcp1h, :tc,
             :ap, :acp, :ac, :rc,
             :ip, :icp, :ic,
             :req, :quota)
        ON CONFLICT (organization_id, model_name, bill_day)
        DO UPDATE SET
            create_time                   = EXCLUDED.create_time,
            text_prompt                   = sp_platform_organization_llm_daily_bill.text_prompt                   + EXCLUDED.text_prompt,
            text_cache_prompt             = sp_platform_organization_llm_daily_bill.text_cache_prompt             + EXCLUDED.text_cache_prompt,
            text_cache_prompt_write5m     = sp_platform_organization_llm_daily_bill.text_cache_prompt_write5m     + EXCLUDED.text_cache_prompt_write5m,
            text_cache_prompt_write1h     = sp_platform_organization_llm_daily_bill.text_cache_prompt_write1h     + EXCLUDED.text_cache_prompt_write1h,
            text_completion               = sp_platform_organization_llm_daily_bill.text_completion               + EXCLUDED.text_completion,
            audio_prompt                  = sp_platform_organization_llm_daily_bill.audio_prompt                  + EXCLUDED.audio_prompt,
            audio_cache_prompt            = sp_platform_organization_llm_daily_bill.audio_cache_prompt            + EXCLUDED.audio_cache_prompt,
            audio_completion              = sp_platform_organization_llm_daily_bill.audio_completion              + EXCLUDED.audio_completion,
            reasoning_completion          = sp_platform_organization_llm_daily_bill.reasoning_completion          + EXCLUDED.reasoning_completion,
            image_prompt                  = sp_platform_organization_llm_daily_bill.image_prompt                  + EXCLUDED.image_prompt,
            image_cache_prompt            = sp_platform_organization_llm_daily_bill.image_cache_prompt            + EXCLUDED.image_cache_prompt,
            image_completion              = sp_platform_organization_llm_daily_bill.image_completion              + EXCLUDED.image_completion,
            request                       = sp_platform_organization_llm_daily_bill.request                       + EXCLUDED.request,
            quota                         = sp_platform_organization_llm_daily_bill.quota                         + EXCLUDED.quota
        """, nativeQuery = true)
    void upsertDaily(@Param("id")        Long id,
                     @Param("orgId")     Long orgId,
                     @Param("modelName")   String modelName,
                     @Param("billDay") Long billDay,
                     @Param("nowMillis") Long nowMillis,
                     @Param("tp")  Long textPrompt,
                     @Param("tcp") Long textCachePrompt,
                     @Param("tcp5") Long textCache5M,
                     @Param("tcp1h") Long textCache1H,
                     @Param("tc")  Long textCompletion,
                     @Param("ap")  Long audioPrompt,
                     @Param("acp") Long audioCachePrompt,
                     @Param("ac")  Long audioCompletion,
                     @Param("rc")  Long reasoningCompletion,
                     @Param("ip")  Long imagePrompt,
                     @Param("icp") Long imageCachePrompt,
                     @Param("ic")  Long imageCompletion,
                     @Param("req") Long req,
                     @Param("quota") BigDecimal quota);
}
