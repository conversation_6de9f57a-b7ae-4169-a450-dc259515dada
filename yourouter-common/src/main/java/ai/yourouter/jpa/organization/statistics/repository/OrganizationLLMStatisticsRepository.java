package ai.yourouter.jpa.organization.statistics.repository;


import ai.yourouter.jpa.organization.statistics.bean.OrganizationLLMStatistics;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

@Repository
public interface OrganizationLLMStatisticsRepository extends JpaRepository<OrganizationLLMStatistics,Long> {

    /* --- 步骤 A：0→2 并聚合返回 --- */
    @Query(value = """
        WITH moved AS (
            UPDATE sp_platform_organization_llm_statistics
               SET statuses = 2
             WHERE statuses      = 0
               AND bucket_start <= :cutOff
             RETURNING id,
                       organization_id,
                       system_model_id,
                       bucket_start,
                       /* ============ 所有计数列 ============ */
                       text_prompt,
                       text_cache_prompt,
                       text_cache_prompt_write5m,
                       text_cache_prompt_write1h,
                       text_completion,
                       audio_prompt,
                       audio_cache_prompt,
                       audio_completion,
                       reasoning_completion,
                       image_prompt,
                       image_cache_prompt,
                       image_completion,
                       quota
        )
        SELECT organization_id,
               system_model_id,
               bucket_start,
               COUNT(*)                          AS reqCnt,
               /* ------- 按列求和 ------- */
               SUM(text_prompt)                 AS textPrompt,
               SUM(text_cache_prompt)           AS textCachePrompt,
               SUM(text_cache_prompt_write5m)   AS textCache5M,
               SUM(text_cache_prompt_write1h)   AS textCache1H,
               SUM(text_completion)             AS textCompletion,
               SUM(audio_prompt)                AS audioPrompt,
               SUM(audio_cache_prompt)          AS audioCachePrompt,
               SUM(audio_completion)            AS audioCompletion,
               SUM(reasoning_completion)        AS reasoningCompletion,
               SUM(image_prompt)                AS imagePrompt,
               SUM(image_cache_prompt)          AS imageCachePrompt,
               SUM(image_completion)            AS imageCompletion,
               SUM(quota)                       AS totalQuota,
               array_agg(id)                    AS ids
        FROM moved
        GROUP BY organization_id, system_model_id, bucket_start
        """, nativeQuery = true)
    List<Object[]> lockAndAggregate(@Param("cutOff") long cutOff);


    /* --- 步骤 C：2→1 --- */
    @Modifying
    @Query(value = """
        UPDATE sp_platform_organization_llm_statistics
           SET statuses = 1
         WHERE id = ANY(:ids)
        """, nativeQuery = true)
    int markDone(@Param("ids") Long[] ids);



    @Query(value = """
    SELECT COALESCE(SUM(quota), 0)
      FROM sp_platform_organization_llm_statistics
     WHERE organization_id = :orgId
    """, nativeQuery = true)
    BigDecimal sumQuotaByOrganizationId(@Param("orgId") Long orgId);


}
