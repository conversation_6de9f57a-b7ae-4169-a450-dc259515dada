package ai.yourouter.jpa.organization.cycle.repository;

import ai.yourouter.jpa.organization.cycle.bean.OrganizationSearchCycle;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;

@Repository
public interface OrganizationSearchCycleRepository extends JpaRepository<OrganizationSearchCycle, Long> {

    @Modifying
    @Query(value = """
        INSERT INTO sp_platform_organization_search_cycle
            (id, organization_id, system_model_id, bucket_start,
             request, call, quota, statuses)
        VALUES
            (:id, :orgId, :modelId, :bucketStart,
             :req, :callCnt, :quota, 0)
        ON CONFLICT (organization_id, system_model_id, bucket_start)
        DO UPDATE
          SET request          = sp_platform_organization_search_cycle.request          + EXCLUDED.request,
              call             = sp_platform_organization_search_cycle.call             + EXCLUDED.call,
              quota            = sp_platform_organization_search_cycle.quota            + EXCLUDED.quota
        """, nativeQuery = true)
    void upsertCycle(@Param("id")        Long       id,
                     @Param("orgId")     Long       orgId,
                     @Param("modelId")   Long       modelId,
                     @Param("bucketStart") Long     bucketStart,
                     @Param("req")       Long       req,
                     @Param("callCnt")   Long       callCnt,
                     @Param("quota") BigDecimal quota);

}
