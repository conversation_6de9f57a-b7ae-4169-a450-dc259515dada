package ai.yourouter.common.utils;

import ai.yourouter.common.constant.RedisKey;
import ai.yourouter.common.constant.SystemConstant;
import ai.yourouter.common.constant.UnpaidStatus;
import ai.yourouter.jpa.organization.transaction.balance.bean.BalanceTransaction;
import ai.yourouter.jpa.organization.transaction.balance.bean.OrganizationBillingStatement;
import ai.yourouter.jpa.organization.transaction.balance.repository.BalanceTransactionRepository;
import ai.yourouter.jpa.organization.transaction.balance.repository.OrganizationBillingStatementRepository;
import ai.yourouter.jpa.stripe.record.bean.StripeChargeRecord;
import ai.yourouter.jpa.stripe.record.repository.StripeChargeRecordRepository;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class OrganizationBalanceUtils {



    private final BalanceTransactionRepository balanceTransactionRepository;

    private final StripeChargeRecordRepository stripeChargeRecordRepository;

    private final OrganizationBillingStatementRepository organizationBillingStatementRepository;

    private final RedisTemplate<String,String> redisTemplate;



    @SneakyThrows
    public Long deductBalance(Long organizationId, long nanosUSD) {

        String key = SystemConstant.ORGANIZATION_REDIS_BALANCE + organizationId;

        /* 1) Redis INCRBY (负数) 原子扣 */
        Long after = redisTemplate.opsForValue().increment(key, -nanosUSD);
        if (after == null) {                            // key 不存在
            log.error("扣费失败，Redis 未找到余额 key，org={}", organizationId);
            return null;
        }

        /* 2) 如果需要透支告警，可在此处做阈值判断 */

        if (after.equals(UnpaidStatus.PENDING)){
            redisTemplate.opsForValue().set(RedisKey.ORGANIZATION_UNPAID_STATUS.getKey(organizationId), String.valueOf(UnpaidStatus.PENDING));
        }

        return after;
    }


    public void addBalance(Long organizationId, long nanosUSD) {
        String key = SystemConstant.ORGANIZATION_REDIS_BALANCE + organizationId;

        /* 1) Redis INCRBY (负数) 原子扣 */
        Long after = redisTemplate.opsForValue().increment(key, nanosUSD);
        if (after == null) {                            // key 不存在
            log.error("扣费失败，Redis 未找到余额 key，org={}", organizationId);
        }

        if (after >= 0){
            redisTemplate.opsForValue().set(RedisKey.ORGANIZATION_UNPAID_STATUS.getKey(organizationId), String.valueOf(UnpaidStatus.UNAVAILABLE));
        }
    }


    /**
     * 扣减组织余额（BigDecimal），顺序：GIFT/ADJUSTMENT → RECHARGE。
     * RECHARGE 的可扣额度 = amount - amountRefunded - amountCaptured。
     * 若 RECHARGE 绑定 Stripe：仅在 status=succeeded && refunded!=true 时允许，并同步回写 Stripe 的 amountCaptured。
     * 成功后更新该组织唯一一条 OrganizationBillingStatement（窗口 = 上次 endTime ~ 本次 now）。
     */
    @Transactional(rollbackFor = Exception.class)
    public void consumeBalance(Long organizationId, BigDecimal amountToConsume) {
        // 1) 入参校验
        if (amountToConsume == null || amountToConsume.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("组织 {} 消费金额非法：{}", organizationId, amountToConsume);
            return;
        }

        BigDecimal remaining = strip(amountToConsume);

        // 2) 先扣 GIFT + ADJUSTMENT
        List<BalanceTransaction> giftAndAdjustment = balanceTransactionRepository.findAvailableBalances(
                organizationId,
                List.of(BalanceTransaction.TransactionType.GIFT, BalanceTransaction.TransactionType.ADJUSTMENT)
        );
        giftAndAdjustment.sort(Comparator.comparing(tx -> tx.getCreated() == null ? 0L : tx.getCreated()));

        for (ListIterator<BalanceTransaction> it = giftAndAdjustment.listIterator();
             it.hasNext() && gt(remaining, BigDecimal.ZERO); ) {
            BalanceTransaction tx = it.next();

            BigDecimal captured  = nz(tx.getAmountCaptured());
            BigDecimal amount    = nz(tx.getAmount());
            BigDecimal available = amount.subtract(captured);
            if (le(available, BigDecimal.ZERO)) continue;

            BigDecimal toCapture = min(available, remaining);
            tx.setAmountCaptured(captured.add(toCapture));
            balanceTransactionRepository.save(tx);

            remaining = remaining.subtract(toCapture);
            log.info("组织 {} 扣减 {} 元，来自 {}（txId={}）", organizationId, toCapture.toPlainString(), tx.getType(), tx.getId());
        }

        // 3) 不足则扣 RECHARGE（含 Stripe 校验与回写）
        if (gt(remaining, BigDecimal.ZERO)) {
            List<BalanceTransaction> rechargeList = balanceTransactionRepository.findAvailableBalances(
                    organizationId,
                    List.of(BalanceTransaction.TransactionType.RECHARGE)
            );
            rechargeList.sort(Comparator.comparing(tx -> tx.getCreated() == null ? 0L : tx.getCreated()));

            // 预取 Stripe 记录
            Map<String, StripeChargeRecord> stripeMap = Collections.emptyMap();
            List<String> stripeIds = rechargeList.stream()
                    .map(BalanceTransaction::getStripeChargeId)
                    .filter(s -> s != null && !s.isBlank())
                    .distinct()
                    .toList();
            if (!stripeIds.isEmpty()) {
                List<StripeChargeRecord> srs = stripeChargeRecordRepository
                        .findByOrganizationIdAndStripeIdIn(organizationId, stripeIds);
                stripeMap = srs.stream().collect(Collectors.toMap(StripeChargeRecord::getStripeId, Function.identity(), (a, b) -> a));
            }

            for (ListIterator<BalanceTransaction> it = rechargeList.listIterator();
                 it.hasNext() && gt(remaining, BigDecimal.ZERO); ) {
                BalanceTransaction tx = it.next();

                BigDecimal amount     = nz(tx.getAmount());
                BigDecimal captured   = nz(tx.getAmountCaptured());
                BigDecimal refunded   = nz(tx.getAmountRefunded());
                BigDecimal localAvail = amount.subtract(refunded).subtract(captured);
                if (le(localAvail, BigDecimal.ZERO)) continue;

                String stripeId = tx.getStripeChargeId();
                if (stripeId != null && !stripeId.isBlank()) {
                    StripeChargeRecord sr = stripeMap.get(stripeId);
                    if (sr == null) {
                        log.warn("组织 {} 的 RECHARGE(txId={}) stripeId={} 未找到 Stripe 记录，跳过。", organizationId, tx.getId(), stripeId);
                        continue;
                    }
                    String status = sr.getStatus() == null ? "" : sr.getStatus();
                    Boolean refundedFlag = sr.getRefunded();
                    if (!"succeeded".equalsIgnoreCase(status) || Boolean.TRUE.equals(refundedFlag)) {
                        log.warn("组织 {} 的 Stripe 不允许扣减（status={}, refunded={}），stripeId={}，跳过。", organizationId, status, refundedFlag, stripeId);
                        continue;
                    }

                    BigDecimal srAmount    = nz(sr.getAmount());
                    BigDecimal srCaptured  = nz(sr.getAmountCaptured());
                    BigDecimal srRefunded  = nz(sr.getAmountRefunded());
                    BigDecimal stripeAvail = srAmount.subtract(srRefunded).subtract(srCaptured);
                    if (le(stripeAvail, BigDecimal.ZERO)) {
                        log.info("组织 {} Stripe 可用为 0（stripeId={}），跳过。", organizationId, stripeId);
                        continue;
                    }

                    BigDecimal toCapture = min(localAvail, stripeAvail, remaining);
                    if (le(toCapture, BigDecimal.ZERO)) continue;

                    // 回写本地
                    tx.setAmountCaptured(captured.add(toCapture));
                    balanceTransactionRepository.save(tx);

                    // 回写 Stripe
                    sr.setAmountCaptured(srCaptured.add(toCapture));
                    stripeChargeRecordRepository.save(sr);

                    remaining = remaining.subtract(toCapture);
                    log.info("组织 {} 扣减 {} 元，来自 RECHARGE（txId={}，stripeId={}）",
                            organizationId, toCapture.toPlainString(), tx.getId(), stripeId);
                } else {
                    // 无 stripeId 的 RECHARGE
                    BigDecimal toCapture = min(localAvail, remaining);
                    if (le(toCapture, BigDecimal.ZERO)) continue;

                    tx.setAmountCaptured(captured.add(toCapture));
                    balanceTransactionRepository.save(tx);

                    remaining = remaining.subtract(toCapture);
                    log.info("组织 {} 扣减 {} 元，来自 RECHARGE(无 Stripe)（txId={}）",
                            organizationId, toCapture.toPlainString(), tx.getId());
                }
            }
        }

        // 4) 余额不足：写 Redis + 抛出异常回滚数据库修改
        if (gt(remaining, BigDecimal.ZERO)) {
            redisTemplate.opsForValue().set(
                    RedisKey.ORGANIZATION_UNPAID_STATUS.getKey(organizationId),
                    String.valueOf(UnpaidStatus.PENDING)
            );
            throw new IllegalStateException(String.format(
                    "组织 %d 余额不足，仍需扣除 %s 元", organizationId, remaining.toPlainString()
            ));
        }

        // 5) 扣减成功：写/更新唯一账单
        persistOrUpdateSingleBillingStatement(organizationId,amountToConsume);

        log.info("组织 {} 金额消费成功，总金额：{} 元，并已更新账单。", organizationId, amountToConsume.toPlainString());
    }


    /**
     * 累计账单模式：
     * - 若存在该组织唯一账单：窗口 = (last.endTime, now]；把窗口“增量”累加到累计字段；仅更新 endTime=now；startTime 保持首次创建时值不变。
     * - 若不存在：创建一条，startTime=endTime=now，createdAt=endTime，累计字段初始化为 0。
     * 说明：窗口是用 BalanceTransaction.created 作为筛选时间。
     */
    @Transactional(rollbackFor = Exception.class)
    public void persistOrUpdateSingleBillingStatement(Long organizationId,BigDecimal amountToConsume) {
        long now = System.currentTimeMillis();

        // 1) 读或初始化“单条累计账单”
        OrganizationBillingStatement stmt = organizationBillingStatementRepository
                .findOrganizationBillingStatementByOrganizationId(organizationId)
                .orElseGet(() -> {
                    OrganizationBillingStatement s = new OrganizationBillingStatement();
                    s.setOrganizationId(organizationId);
                    s.setStartTime(now);                       // 累计起点只在首次设置
                    s.setCreatedAt(Instant.ofEpochMilli(now)); // 首次创建时间
                    s.setTotalRecharge(BigDecimal.ZERO);
                    s.setTotalGift(BigDecimal.ZERO);
                    s.setTotalAdjustment(BigDecimal.ZERO);
                    s.setTotalCaptured(BigDecimal.ZERO);
                    s.setTotalRefunded(BigDecimal.ZERO);
                    s.setNetBalanceChange(BigDecimal.ZERO);
                    return s;
                });

        // 2) —— 部分处理 + 重新计算净入账—— //

        stmt.setTotalCaptured(nz(stmt.getTotalCaptured()).add(nz(amountToConsume)));


        /** 净入账金额 = 充值 + 赠送 + 调账 - 消费 - 退款 */
        stmt.setNetBalanceChange(stmt.getNetBalanceChange().subtract(nz(amountToConsume)));

        // 4) 仅更新 endTime 为当前
        stmt.setEndTime(now);

        organizationBillingStatementRepository.save(stmt);

        log.info("BillingStatement ΔCaptured[org={}] +{} -> totalCaptured={}, net={}",
                organizationId, amountToConsume.toPlainString(),
                stmt.getTotalCaptured().toPlainString(),
                stmt.getNetBalanceChange().toPlainString());
    }


    private static BigDecimal nz(BigDecimal v) { return v == null ? BigDecimal.ZERO : v; }
    private static BigDecimal strip(BigDecimal v) { return v == null ? BigDecimal.ZERO : v.stripTrailingZeros(); }
    private static boolean gt(BigDecimal a, BigDecimal b) { return a.compareTo(b) > 0; }
    private static boolean le(BigDecimal a, BigDecimal b) { return a.compareTo(b) <= 0; }
    private static BigDecimal min(BigDecimal a, BigDecimal b) { return a.min(b); }
    private static BigDecimal min(BigDecimal a, BigDecimal b, BigDecimal c) { return a.min(b).min(c); }
    private static String s(BigDecimal v) { return nz(v).toPlainString(); }
    private static String toStr(BigDecimal v) {
        return nz(v).toPlainString();
    }

    /**
     * 通用求和：对集合中满足 filter 的元素，取出 BigDecimal 字段并相加（自动做 null 安全）。
     */
    private static <T> BigDecimal sum(Collection<T> list,
                                      Predicate<T> filter,
                                      Function<T, BigDecimal> mapper) {
        BigDecimal acc = BigDecimal.ZERO;
        if (list == null || list.isEmpty()) return acc;
        for (T item : list) {
            if (filter.test(item)) {
                acc = acc.add(nz(mapper.apply(item)));
            }
        }
        return acc;
    }
}
