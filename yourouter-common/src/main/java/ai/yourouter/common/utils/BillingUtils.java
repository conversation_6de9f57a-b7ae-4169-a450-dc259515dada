package ai.yourouter.common.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

public final class BillingUtils {


    /** 1 USD = 1_000_000_000 nano-dollar */
    private static final long NANOS_PER_DOLLAR = 100_000_000_000L;
    /** 单位换算：每百万 tokens */
    private static final long TOKENS_PER_MILLION = 1_000_000L;

    private static final BigDecimal NANOCENT_PER_USD = new BigDecimal("100000000000");


    private BillingUtils() {}

    /**
     * 计算总费用（返回 nano-dollar，long）。
     *
     * @param inTokens      输入 token 数
     * @param cacheTokens   缓存 token 数
     * @param outTokens     输出 token 数
     * @param priceInUSD    输入部分单价（USD / 百万 tokens）
     * @param priceCacheUSD 缓存部分单价（USD / 百万 tokens）
     * @param priceOutUSD   输出部分单价（USD / 百万 tokens）
     * @return 总费用（nano-dollar）
     */
    public static long calcCostNanos(Long inTokens,
                                     Long cacheTokens,
                                     Long outTokens,
                                     BigDecimal priceInUSD,
                                     BigDecimal priceCacheUSD,
                                     BigDecimal priceOutUSD) {

        BigDecimal nanosIn    = partCostNanos(inTokens,  priceInUSD);
        BigDecimal nanosCache = partCostNanos(cacheTokens, priceCacheUSD);
        BigDecimal nanosOut   = partCostNanos(outTokens,   priceOutUSD);


        // longValueExact() 若结果超出 long 会抛异常，保证调用方及时发现
        return nanosIn.add(nanosCache).add(nanosOut).longValueExact();
    }

    /** 将 nano-dollar 转成美元（6 位小数以上时会全部保留） */
    public static BigDecimal nanosToUSD(long nanos) {
        return BigDecimal.valueOf(nanos)
                .divide(BigDecimal.valueOf(NANOS_PER_DOLLAR), 9, RoundingMode.UP);
    }

    public static Long usdToNanoCent(BigDecimal usdAmount) {
        if (usdAmount == null) {
            throw new IllegalArgumentException("Amount must not be null");
        }
        BigDecimal nanoCent = usdAmount.multiply(NANOCENT_PER_USD);

        return nanoCent.setScale(9, RoundingMode.UNNECESSARY).longValueExact();
    }




    /** 计算某一部分 token 的 nano-dollar 费用 */
    private static BigDecimal partCostNanos(Long tokens, BigDecimal priceUSDperM) {
        if (tokens == null || tokens == 0 || priceUSDperM == null || priceUSDperM.signum() == 0) return BigDecimal.ZERO;

        // 单价 USD → nano-dollar
        BigDecimal nanoPricePerM = priceUSDperM.multiply(BigDecimal.valueOf(NANOS_PER_DOLLAR));

        // (token × nanoPricePerM) / 1_000_000  ⇒ nano-dollar
        return nanoPricePerM
                .multiply(BigDecimal.valueOf(tokens))
                .divide(BigDecimal.valueOf(TOKENS_PER_MILLION), 0, RoundingMode.HALF_UP);
    }

    public static void main(String[] args) {
        //System.out.println(BillingUtils.calcCostNanos(1000,0L,0,BigDecimal.valueOf(5L),BigDecimal.ZERO,BigDecimal.valueOf(0L)));
        long nanos = BillingUtils.calcCostNanos(
                1_952_558L,                // 输入 tokens
                0L,                        // 缓存 tokens
                39_035L,                   // 输出 tokens
                new BigDecimal("5"),       // $5 / 百万
                BigDecimal.ZERO,           // $0
                new BigDecimal("15"));     // $15 / 百万

        System.out.println("nano-dollar: " + nanos);                    // 10348315000
        System.out.println("USD: " + BillingUtils.nanosToUSD(809600000));   // 10.348315000

        Long nanoCent = usdToNanoCent(BillingUtils.nanosToUSD(nanos));
        System.out.println(BillingUtils.nanosToUSD(nanos) + " USD  →  " + nanoCent + " 纳美分");

        System.out.println(usdToNanoCent(BigDecimal.valueOf(5.808096)));
    }

}

