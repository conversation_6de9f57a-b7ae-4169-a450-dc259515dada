plugins {
    id 'java-library'
    id 'org.springframework.boot' version '3.5.3'
    id 'io.spring.dependency-management' version '1.1.7'
}

group = 'ai.yourouter'
version = '1.0-SNAPSHOT'

repositories {
    mavenCentral()
}

dependencies {
    api 'org.projectlombok:lombok'
    api 'org.apache.commons:commons-pool2'
    annotationProcessor 'org.projectlombok:lombok'
    api 'org.springframework.boot:spring-boot-starter'

    api 'org.springframework.boot:spring-boot-starter-data-jpa'
    api 'org.springframework.boot:spring-boot-starter-data-redis'

}

test {
    useJUnitPlatform()
}