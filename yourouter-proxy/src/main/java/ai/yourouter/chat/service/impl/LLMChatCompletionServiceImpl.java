package ai.yourouter.chat.service.impl;

import ai.yourouter.calculate.llm.OrganizationLLMCalculatePrice;
import ai.yourouter.chat.channel.OpenaiRemoteService;
import ai.yourouter.chat.config.RetryConfig;
import ai.yourouter.chat.remote.KmgRemoteService;
import ai.yourouter.chat.util.ChatLogUtils;
import ai.yourouter.common.context.ChatContext;
import ai.yourouter.common.service.AbstractRemoteServiceTemplate;
import ai.yourouter.common.service.AsyncUsageStatisticsService;
import ai.yourouter.common.utils.CharacterLLMStatisticsConverter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class LLMChatCompletionServiceImpl extends AbstractRemoteServiceTemplate {

    private final OpenaiRemoteService openaiRemoteService;
    private final OrganizationLLMCalculatePrice organizationLLMCalculatePrice;
    private final AsyncUsageStatisticsService asyncUsageStatisticsService;

    public LLMChatCompletionServiceImpl(RetryConfig retryConfig,
                                        KmgRemoteService kmgRemoteService,
                                        OpenaiRemoteService openaiRemoteService,
                                        OrganizationLLMCalculatePrice organizationLLMCalculatePrice,
                                        AsyncUsageStatisticsService asyncUsageStatisticsService) {
        super(retryConfig, kmgRemoteService);
        this.openaiRemoteService = openaiRemoteService;
        this.organizationLLMCalculatePrice = organizationLLMCalculatePrice;
        this.asyncUsageStatisticsService = asyncUsageStatisticsService;
    }


    @SneakyThrows
    public Mono<Object> completion(HashMap<String, Object> req, Map<String, String> params) {
        return Mono.deferContextual(contextView -> {
            ChatContext chatContext = extractChatContext(contextView, req);
            chatContext.getChatRequestStatistic().setRequestParams(params);
            String modelName = req.get("model").toString();
            if (chatContext.getChatModelInfo().onClaude()) {
                chatContext.getChatRequestStatistic().setVendorHeader("Anthropic");
            }
            Mono<Object> operation = getAvailableKey(modelName, chatContext)
                    .flatMap(key -> {
                        chatContext.setKeyInfo(key);
                        return openaiRemoteService.nonStreamOpenAiCompletion(req, chatContext);
                    });

            return executeWithRetry(operation, "非流式请求", chatContext);
        });
    }

    @SneakyThrows
    public Flux<ServerSentEvent<String>> streamCompletion(HashMap<String, Object> req, Map<String, String> params) {
        return Flux.deferContextual(contextView -> {
            ChatContext chatContext = extractChatContext(contextView, req);
            chatContext.getChatRequestStatistic().setRequestParams(params);
            String modelName = req.get("model").toString();
            if (chatContext.getChatModelInfo().onClaude()) {
                chatContext.getChatRequestStatistic().setVendorHeader("Anthropic");
            }
            Flux<ServerSentEvent<String>> streamOperation = getAvailableKeyForStream(modelName, chatContext)
                    .flatMapMany(key -> {
                        chatContext.setKeyInfo(key);
                        return openaiRemoteService.streamOpenAiCompletion(req, chatContext);
                    });

            return executeStreamWithClientManagement(streamOperation, "流式请求", chatContext);
        });
    }

    /**
     * 提取聊天上下文并设置请求信息
     */
    private ChatContext extractChatContext(reactor.util.context.ContextView contextView, HashMap<String, Object> req) {
        ChatContext chatContext = contextView.get("chatContext");
        chatContext.getChatRequestStatistic().setRawRequest(req);
        return chatContext;
    }

    @Override
    protected void publishUsageStatistics(ChatContext chatContext) {
        try {
            organizationLLMCalculatePrice.organizationListen(
                    CharacterLLMStatisticsConverter.convert(chatContext),
                    chatContext.apiModelName()
            );
            ChatLogUtils.logUsageStatistics(chatContext, true);
        } catch (Exception e) {
            ChatLogUtils.logUsageStatistics(chatContext, false);
            log.error("发布使用量统计详细错误信息", e);
        }
    }

    @Override
    protected void publishUsageStatisticsAsync(ChatContext chatContext) {
        // 使用智能选择方法，根据配置自动决定异步或同步处理
        asyncUsageStatisticsService.publishLLMUsageStatisticsIntelligent(
                chatContext,
                () -> publishUsageStatistics(chatContext)
        );
    }

}
