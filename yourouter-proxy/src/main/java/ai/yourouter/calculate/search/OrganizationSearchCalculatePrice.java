package ai.yourouter.calculate.search;

import ai.yourouter.common.constant.PermissionStatusEnum;
import ai.yourouter.common.constant.SystemConstant;
import ai.yourouter.common.utils.BillingUtils;
import ai.yourouter.common.utils.OrganizationBalanceUtils;
import ai.yourouter.common.utils.SnowflakeIdGenerator;
import ai.yourouter.jpa.organization.statistics.bean.OrganizationSearchStatistics;
import ai.yourouter.jpa.organization.statistics.repository.OrganizationSearchStatisticsRepository;
import ai.yourouter.jpa.organization.transaction.failed.bean.FailedTransaction;
import ai.yourouter.jpa.organization.transaction.failed.repository.FailedTransactionRepository;
import ai.yourouter.jpa.system.model.price.bean.SystemSearchPrice;
import ai.yourouter.jpa.system.model.price.repository.SystemSearchPriceRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Slf4j
@Component
@RequiredArgsConstructor
public class OrganizationSearchCalculatePrice {

    private final ObjectMapper objectMapper;

    private final SnowflakeIdGenerator snowflakeIdGenerator;

    private final OrganizationBalanceUtils organizationBalanceUtils;

    private final FailedTransactionRepository failedTransactionRepository;

    private final SystemSearchPriceRepository systemSearchPriceRepository;

    private final OrganizationSearchStatisticsRepository organizationSearchStatisticsRepository;




    @Transactional(rollbackOn = {Throwable.class})
    public void organizationListen(OrganizationSearchStatistics organizationSearchStatistics) {

        try {
            SystemSearchPrice searchPrice = systemSearchPriceRepository.findSystemSearchPriceBySystemModelIdAndStatuses(organizationSearchStatistics.getSystemModelId(), PermissionStatusEnum.AVAILABLE.getCode());
            //这里计费
            long nanosUSD = calculateQuota(organizationSearchStatistics, searchPrice);
            BigDecimal quota = BillingUtils.nanosToUSD(nanosUSD);
            organizationSearchStatistics.setQuota(quota);
            organizationSearchStatistics.setStatuses(PermissionStatusEnum.UNAVAILABLE.getCode());
            organizationSearchStatistics.setId(snowflakeIdGenerator.nextId());
            organizationSearchStatisticsRepository.saveAndFlush(organizationSearchStatistics);
            organizationBalanceUtils.deductBalance(organizationSearchStatistics.getOrganizationId(),nanosUSD);
        }catch (Exception e) {
            log.error("Failed to process organizationListen for organizationId: " + organizationSearchStatistics.getOrganizationId(), e);
            // 可选择记录到数据库，或其他地方以便后续恢复
            recordFailedTransaction(organizationSearchStatistics, e);
            throw e; // 重新抛出异常，保证事务回滚
        }
    }


    private Long calculateQuota(OrganizationSearchStatistics statistics, SystemSearchPrice price) {
        return partCostNanos(statistics.getCall(),price.getCall()).longValueExact();
    }


    /** 计算某一部分 token 的 nano-dollar 费用 */
    private static BigDecimal partCostNanos(long tokens, BigDecimal priceUSDperM) {
        if (tokens == 0 || priceUSDperM.signum() == 0) return BigDecimal.ZERO;

        // 单价 USD → nano-dollar
        BigDecimal nanoPricePerM = priceUSDperM.multiply(BigDecimal.valueOf(SystemConstant.NANOS_PER_DOLLAR));

        // (token × nanoPricePerM) / 1_000_000  ⇒ nano-dollar
        return nanoPricePerM
                .multiply(BigDecimal.valueOf(tokens))
                .divide(BigDecimal.valueOf(SystemConstant.CALL_PER_KILO), 0, RoundingMode.HALF_UP);
    }

    // 记录失败交易的辅助方法
    @SneakyThrows
    private void recordFailedTransaction(OrganizationSearchStatistics organizationSearchStatistics, Exception e) {
        // 这里可以将相关信息记录到一个专门用于记录失败交易的表中
        FailedTransaction failedTransaction = new FailedTransaction();
        failedTransaction.setOrganizationId(organizationSearchStatistics.getOrganizationId());
        failedTransaction.setMessages(objectMapper.writeValueAsString(organizationSearchStatistics));
        failedTransaction.setErrorMessage(e.getMessage());
        failedTransaction.setCreateTime(organizationSearchStatistics.getCreateTime());
        failedTransactionRepository.saveAndFlush(failedTransaction);
    }

}
