package ai.yourouter.common.service;

import ai.yourouter.chat.config.RetryConfig;
import ai.yourouter.chat.remote.KmgRemoteService;
import ai.yourouter.chat.remote.response.BestKeyResponse;
import ai.yourouter.chat.util.ChatLogUtils;
import ai.yourouter.common.context.ChatContext;
import ai.yourouter.common.exception.CognitionWebException;
import ai.yourouter.common.exception.RetryableCognitionWebException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.retry.Retry;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 远程服务调用的抽象模板类
 * 提供公共的重试逻辑、错误处理和使用量统计功能
 */
@Slf4j
@RequiredArgsConstructor
public abstract class AbstractRemoteServiceTemplate {

    protected final RetryConfig retryConfig;
    protected final KmgRemoteService kmgRemoteService;

    /**
     * 执行带重试的远程调用
     */
    protected <T> Mono<T> executeWithRetry(Mono<T> operation, String requestType, ChatContext chatContext) {
        Mono<T> result = operation
                .doOnError(error -> logCompletionError(error, chatContext))
                .publishOn(Schedulers.boundedElastic())
                .doOnSuccess(response -> publishUsageStatisticsAsync(chatContext));

        return applyRetryIfEnabled(result, requestType, chatContext);
    }

    /**
     * 执行带重试的流式远程调用
     */
    protected <T> Flux<T> executeStreamWithRetry(Flux<T> operation, String requestType, ChatContext chatContext) {
        return applyRetryIfEnabled(operation, requestType, chatContext);
    }

    /**
     * 应用重试逻辑（如果启用）- Mono版本
     */
    private <T> Mono<T> applyRetryIfEnabled(Mono<T> mono, String requestType, ChatContext chatContext) {
        if (retryConfig.isEnabled()) {
            return mono.retryWhen(createRetrySpec(requestType, chatContext));
        }
        return mono;
    }

    /**
     * 应用重试逻辑（如果启用）- Flux版本
     */
    private <T> Flux<T> applyRetryIfEnabled(Flux<T> flux, String requestType, ChatContext chatContext) {
        if (retryConfig.isEnabled()) {
            return flux.retryWhen(createRetrySpec(requestType, chatContext));
        }
        return flux;
    }

    /**
     * 创建重试规范
     */
    private Retry createRetrySpec(String requestType, ChatContext chatContext) {
        return Retry.backoff(retryConfig.getMaxAttempts(), retryConfig.getInitialDelay())
                .maxBackoff(retryConfig.getMaxDelay())
                .multiplier(retryConfig.getMultiplier())
                .filter(this::shouldRetry)
                .doBeforeRetry(retrySignal -> {
                    long attempt = retrySignal.totalRetries() + 1;
                    Throwable failure = retrySignal.failure();
                    ChatLogUtils.logRetryAttempt(requestType, chatContext, attempt, failure);

                    // 增加重试计数
                    chatContext.setRetryCount(chatContext.getRetryCount() + 1);
                })
                .doAfterRetry(retrySignal -> {
                    long attempt = retrySignal.totalRetries() + 1;
                    ChatLogUtils.logRetryComplete(requestType, chatContext, attempt);
                })
                .onRetryExhaustedThrow((retryBackoffSpec, retrySignal) -> {
                    ChatLogUtils.logRetryExhausted(requestType, chatContext, retrySignal.totalRetries(), retrySignal.failure());
                    return retrySignal.failure();
                });
    }

    /**
     * 判断是否应该重试
     */
    private boolean shouldRetry(Throwable throwable) {
        // 如果是RetryableCognitionWebException，根据其retryable属性决定
        if (throwable instanceof RetryableCognitionWebException retryableEx) {
            boolean shouldRetry = retryableEx.isRetryable();
            log.debug("RetryableCognitionWebException重试判断 | 可重试: {} | 原因: {}",
                    shouldRetry, retryableEx.getAnalysisReason());
            return shouldRetry;
        }

        // 不重试普通的CognitionWebException
        if (throwable instanceof CognitionWebException) {
            log.debug("CognitionWebException不重试");
            return false;
        }

        // 不重试400状态码的WebClientResponseException
        if (throwable instanceof WebClientResponseException ex) {
            boolean shouldRetry = ex.getStatusCode().value() != 400;
            log.debug("WebClientResponseException重试判断 | 状态码: {} | 可重试: {}",
                    ex.getStatusCode().value(), shouldRetry);
            return shouldRetry;
        }

        // 其他异常可以重试
        log.debug("其他异常可以重试: {}", throwable.getClass().getSimpleName());
        return true;
    }

    /**
     * 记录完成错误日志
     */
    protected void logCompletionError(Throwable error, ChatContext chatContext) {
        ChatLogUtils.logRequestError(error, chatContext, false);
    }

    /**
     * 记录请求开始日志
     */
    protected void logRequestStart(ChatContext chatContext, Object bestKey, boolean isStream) {
        ChatLogUtils.logRequestStart(chatContext, bestKey, isStream);
    }

    /**
     * 发布使用量统计 - 抽象方法，由子类实现具体的统计逻辑
     */
    protected abstract void publishUsageStatistics(ChatContext chatContext);

    /**
     * 异步发布使用量统计 - 抽象方法，由子类实现具体的异步统计逻辑
     */
    protected abstract void publishUsageStatisticsAsync(ChatContext chatContext);

    /**
     * 获取可用密钥的通用方法
     */
    protected Mono<BestKeyResponse> getAvailableKey(ChatContext chatContext) {
        String vendorHeader = chatContext.getChatRequestStatistic().vendorHeader();
        return kmgRemoteService.getAvailable(chatContext.apiModelName(), vendorHeader)
                .doOnNext(bestKey -> logRequestStart(chatContext, bestKey, false));
    }

    /**
     * 获取可用密钥的通用方法（带模型名称参数）
     */
    protected Mono<BestKeyResponse> getAvailableKey(String modelName, ChatContext chatContext) {
        String vendorHeader = chatContext.getChatRequestStatistic().vendorHeader();
        return kmgRemoteService.getAvailable(modelName, vendorHeader)
                .doOnNext(bestKey -> logRequestStart(chatContext, bestKey, false));
    }

    /**
     * 获取可用密钥的通用方法（流式请求）
     */
    protected Mono<BestKeyResponse> getAvailableKeyForStream(ChatContext chatContext) {
        String vendorHeader = chatContext.getChatRequestStatistic().vendorHeader();
        return kmgRemoteService.getAvailable(chatContext.apiModelName(), vendorHeader)
                .doOnNext(bestKey -> logRequestStart(chatContext, bestKey, true));
    }

    /**
     * 获取可用密钥的通用方法（流式请求，带模型名称参数）
     */
    protected Mono<BestKeyResponse> getAvailableKeyForStream(String modelName, ChatContext chatContext) {
        String vendorHeader = chatContext.getChatRequestStatistic().vendorHeader();
        return kmgRemoteService.getAvailable(modelName, vendorHeader)
                .doOnNext(bestKey -> logRequestStart(chatContext, bestKey, true));
    }

    /**
     * 执行流式处理，包含客户端连接状态管理
     */
    protected Flux<ServerSentEvent<String>> executeStreamWithClientManagement(
            Flux<ServerSentEvent<String>> streamOperation,
            String requestType,
            ChatContext chatContext) {

        var isClientConnected = new AtomicBoolean(true);

        return Flux.create(fluxSink -> {
            // 应用重试逻辑
            Flux<ServerSentEvent<String>> streamFlow = executeStreamWithRetry(streamOperation, requestType, chatContext);

            streamFlow
                    .doOnNext(response -> handleStreamResponse(response, fluxSink, isClientConnected))
                    .doOnError(error -> handleStreamError(error, fluxSink, isClientConnected, chatContext))
                    .publishOn(Schedulers.boundedElastic())
                    .doOnComplete(() -> handleStreamComplete(fluxSink, isClientConnected, chatContext))
                    .subscribe();

            fluxSink.onCancel(() -> isClientConnected.set(false));
        });
    }

    /**
     * 处理流式响应
     */
    private void handleStreamResponse(ServerSentEvent<String> response,
                                      reactor.core.publisher.FluxSink<ServerSentEvent<String>> fluxSink,
                                      AtomicBoolean isClientConnected) {
        if (isClientConnected.get()) {
            try {
                fluxSink.next(response);
            } catch (Exception e) {
                log.debug("发送流式响应失败，客户端可能已断开连接: {}", e.getMessage());
            }
        }
    }

    /**
     * 处理流式错误
     */
    private void handleStreamError(Throwable error,
                                   reactor.core.publisher.FluxSink<ServerSentEvent<String>> fluxSink,
                                   AtomicBoolean isClientConnected,
                                   ChatContext chatContext) {
        logStreamError(error, chatContext);
        if (isClientConnected.get()) {
            fluxSink.error(error);
        }
    }

    /**
     * 处理流式完成
     */
    private void handleStreamComplete(reactor.core.publisher.FluxSink<ServerSentEvent<String>> fluxSink,
                                      AtomicBoolean isClientConnected,
                                      ChatContext chatContext) {
        if (isClientConnected.get()) {
            fluxSink.complete();
        }
        publishUsageStatisticsAsync(chatContext);
    }

    /**
     * 记录流式错误日志
     */
    protected void logStreamError(Throwable error, ChatContext chatContext) {
        ChatLogUtils.logRequestError(error, chatContext, true);
    }
}
