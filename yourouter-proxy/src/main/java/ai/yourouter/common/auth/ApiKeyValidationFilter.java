package ai.yourouter.common.auth;

import ai.yourouter.common.constant.PermissionStatusEnum;
import ai.yourouter.common.constant.RedisKey;
import ai.yourouter.common.constant.SystemConstant;
import ai.yourouter.common.constant.UnpaidStatus;
import ai.yourouter.common.context.ChatContext;
import ai.yourouter.common.context.ChatModelInfo;
import ai.yourouter.common.context.ChatUserInfo;
import ai.yourouter.common.utils.IpKeyMonitorUtil;
import ai.yourouter.common.utils.SnowflakeIdGenerator;
import ai.yourouter.common.utils.TraceUtils;
import ai.yourouter.jpa.organization.log.bean.OrganizationRequestRecord;
import ai.yourouter.jpa.organization.log.repository.OrganizationRequestRecordRepository;
import ai.yourouter.jpa.organization.secret.bean.OrganizationSecret;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Optional;

@Slf4j
@Component
@RequiredArgsConstructor
@Order(Ordered.HIGHEST_PRECEDENCE)
@SuppressWarnings({"NullableProblems"})
public class ApiKeyValidationFilter implements WebFilter {

    private static final String BEARER_PREFIX = "Bearer ";
    private static final String DASHBOARD_URL = "https://platform.yourouter.ai";

    private static final String ATTR_URI = "REQUEST_URI";
    private static final String ATTR_BODY = "REQUEST_BODY";
    private static final String ATTR_MODEL = "MODEL";
    private static final String ATTR_ORG_ID = "ORGANIZATION_ID";

    private final ObjectMapper objectMapper;

    private final IpKeyMonitorUtil ipKeyMonitorUtil;

    private final RedisTemplate<String, String> redisTemplate;

    private final SnowflakeIdGenerator snowflakeIdGenerator;

    public static final String geminiPrefix = "/v1/projects/cognition/locations/us/publishers/google/models/";





    @Override
    public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
        ServerHttpRequest req = exchange.getRequest();
        ServerHttpResponse resp = exchange.getResponse();
        String uri = req.getURI().toString();
        String traceId = TraceUtils.traceId();
        resp.getHeaders().add("x-request-id", traceId);



        return DataBufferUtils.join(req.getBody())
                .defaultIfEmpty(resp.bufferFactory().allocateBuffer(0))
                .flatMap(buf -> {
                    String model = null;
                    var request = exchange.getRequest();
                    var chatContext = ChatContext.create(new HashMap<>(exchange.getRequest().getHeaders().asSingleValueMap()), traceId);
                    String path = request.getPath().value();

                    // 读取请求体
                    byte[] bytes = new byte[buf.readableByteCount()];
                    buf.read(bytes);
                    DataBufferUtils.release(buf);
                    String body = new String(bytes, StandardCharsets.UTF_8);

                    exchange.getAttributes().put(ATTR_URI, uri);
                    exchange.getAttributes().put(ATTR_BODY, body);

                    if (path.startsWith("/v7.0")) {
                        var tier = Optional.ofNullable(request.getHeaders().getFirst("x-bing-search-tier")).orElse("s2");
                        model = "bing-search-" + tier;
                    } else if (path.startsWith("/customsearch/v1")) {
                        model = "google-search";
                    } else if (path.startsWith(geminiPrefix)) {
                        // 解析 Gemini 原生 API 路径中的模型名称
                        // 路径格式: /v1/projects/cognition/locations/us/publishers/google/models/{model}:generateContent
                        // 或: /v1/projects/cognition/locations/us/publishers/google/models/{model}:streamGenerateContent
                        String remaining = path.substring(geminiPrefix.length());
                        // 提取模型名称（在冒号之前的部分）
                        int colonIndex = remaining.indexOf(':');
                        if (colonIndex > 0) {
                            model = remaining.substring(0, colonIndex);
                        }
                    } else {
                        // 对于其他路径，尝试从请求体中解析模型名称
                        if (MediaType.APPLICATION_JSON.isCompatibleWith(req.getHeaders().getContentType()) && StringUtils.hasText(body)) {
                            try {
                                JsonNode root = objectMapper.readTree(body);
                                if (root.has("model") && root.get("model").isTextual()) {
                                    model = root.get("model").asText();
                                    exchange.getAttributes().put(ATTR_MODEL, model);
                                    log.debug("Request body {}", model);
                                }
                            } catch (Exception e) {
                                log.debug("Request body is not valid JSON", e);
                            }
                        }
                    }

                    // 为所有需要请求体的路径创建缓存的请求体装饰器
                    Flux<DataBuffer> cachedBody = Flux.defer(() -> Mono.just(resp.bufferFactory().wrap(bytes)));
                    ServerHttpRequest wrapped = new ServerHttpRequestDecorator(req) {
                        @Override
                        public Flux<DataBuffer> getBody() {
                            return cachedBody;
                        }
                    };
                    return authenticate(exchange.mutate().request(wrapped).build(), model, resp, chain, chatContext).contextWrite(ctx -> ctx.put("exchange", exchange).put("chatContext", chatContext));
                });
    }

    @SneakyThrows
    private Mono<Void> authenticate(ServerWebExchange ex, String model, ServerHttpResponse resp, WebFilterChain chain, ChatContext chatContext) {
        String apiKey = resolveApiKey(ex.getRequest().getHeaders());
        if (!StringUtils.hasText(apiKey)) {
            return writeError(resp, HttpStatus.UNAUTHORIZED, "Missing or invalid API key. Please subscribe at " + DASHBOARD_URL + ".");
        }

        String json = redisTemplate.opsForValue().get(SystemConstant.PROXY_ORGANIZATION_KEY + apiKey);
        if (!StringUtils.hasText(json)) {
            return writeError(resp, HttpStatus.UNAUTHORIZED, "Invalid API key. Please subscribe at " + DASHBOARD_URL + ".");
        }

        OrganizationSecret org;
        try {
            org = objectMapper.readValue(json, OrganizationSecret.class);
        } catch (Exception e) {
            log.error("Failed to deserialize OrganizationSecret", e);
            return writeError(resp, HttpStatus.UNAUTHORIZED, "Invalid API key data. Please contact support.");
        }

        if (org == null || org.getStatuses() == PermissionStatusEnum.UNAVAILABLE.getCode()) {
            return writeError(resp, HttpStatus.UNAUTHORIZED, "API key disabled. Please subscribe at " + DASHBOARD_URL + ".");
        }

        String permission = redisTemplate.opsForValue().get(RedisKey.ORGANIZATION_PERMISSION_STATUS.getKey(org.getOrganizationId()));
        if (!String.valueOf(PermissionStatusEnum.AVAILABLE.getCode()).equals(permission)) {
            return writeError(resp, HttpStatus.UNAUTHORIZED, "Your organization has been banned. <NAME_EMAIL>.");
        }


        String unpaid = redisTemplate.opsForValue().get(RedisKey.ORGANIZATION_UNPAID_STATUS.getKey(org.getOrganizationId()));
        if (!String.valueOf(UnpaidStatus.UNAVAILABLE).equals(unpaid)) {
            return writeError(resp, HttpStatus.TOO_MANY_REQUESTS, "Insufficient balance. Please top up at " + DASHBOARD_URL + ".");
        }

        if (Boolean.FALSE.equals(redisTemplate.opsForSet().isMember(SystemConstant.PROXY_MODEL_LIST_KEY, model))) {
            return writeError(resp, HttpStatus.NOT_FOUND, "Permission denied for model '" + model + "'. Upgrade at " + DASHBOARD_URL + ".");
        }

        String systemModelId = redisTemplate.opsForValue().get(SystemConstant.PROXY_MODEL_KEY + model);
        if (systemModelId == null) {
            return writeError(resp, HttpStatus.NOT_FOUND, "Permission denied for model '" + model + "'. Upgrade at " + DASHBOARD_URL + ".");
        }

        chatContext.setChatUserInfo(new ChatUserInfo(org.getOrganizationId(), apiKey));
        chatContext.setChatModelInfo(new ChatModelInfo(Long.parseLong(systemModelId), model));
        ServerHttpRequest decorated = ex.getRequest()
                .mutate()
                .header("X-Organization-Id", String.valueOf(org.getOrganizationId()))
                .header("X-Model-Id", systemModelId)// NEW
                .build();

        ex.getAttributes().put(ATTR_ORG_ID, org.getOrganizationId());

        ServerHttpRequest req = ex.getRequest();

        String ip = getClientIp(req);
        String userAgent = req.getHeaders().getFirst("User-Agent");
        String cfConnectingIp = req.getHeaders().getFirst("CF-Connecting-IP");
        String cfIpCountry = req.getHeaders().getFirst("CF-IPCountry");
        String cfRay = req.getHeaders().getFirst("CF-Ray");

        OrganizationRequestRecord record = OrganizationRequestRecord.builder()
                .id(snowflakeIdGenerator.nextId())
                .ip(ip)
                .organizationId(org.getOrganizationId())
                .userAgent(userAgent)
                .cfConnectingIp(cfConnectingIp)
                .cfIpCountry(cfIpCountry)
                .cfRay(cfRay)
                .build();

        ipKeyMonitorUtil.recordAndCheckAbnormal(record);


        return chain.filter(ex.mutate().request(decorated).build());
    }

    private static String resolveApiKey(HttpHeaders headers) {
        String auth = headers.getFirst(HttpHeaders.AUTHORIZATION);
        if (StringUtils.hasText(auth) && auth.startsWith(BEARER_PREFIX)) {
            return auth.substring(BEARER_PREFIX.length()).trim();
        }
        return headers.getFirst("x-api-key");
    }

    private Mono<Void> writeError(ServerHttpResponse resp, HttpStatus status, String message) {
        resp.setStatusCode(status);
        resp.getHeaders().setContentType(MediaType.APPLICATION_JSON);
        String json = String.format("{\"code\":%d,\"message\":\"%s\"}", status.value(), message);
        return resp.writeWith(Mono.just(resp.bufferFactory().wrap(json.getBytes(StandardCharsets.UTF_8))));
    }

    private String getClientIp(ServerHttpRequest request) {
        String cfIp = request.getHeaders().getFirst("CF-Connecting-IP");
        if (cfIp != null) return cfIp;

        String forwardedIp = request.getHeaders().getFirst("X-Forwarded-For");
        if (forwardedIp != null) return forwardedIp.split(",")[0].trim();

        return request.getRemoteAddress() != null
                ? request.getRemoteAddress().getAddress().getHostAddress()
                : "unknown";
    }
}
